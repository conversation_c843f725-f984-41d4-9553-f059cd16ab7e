import 'dart:io';
import 'dart:ui' as ui;
import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image/image.dart' as img;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../models/watermark_model.dart';
// import '../models/photo_model.dart'; // 暂时不使用
import '../utils/constants.dart';
import '../providers/debug_provider.dart';

// 图像处理服务类
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  // 合成带水印的图像（优化版本）
  Future<File> compositeImageWithWatermarks({
    required File originalImage,
    required List<WatermarkModel> watermarks,
    required Size previewSize,
    required Size actualPreviewSize,
    required Offset previewOffset,
    required Size imageSize,
    Function(double)? onProgress,
    DebugProvider? debugProvider,
    Map<String, img.Image>? cachedWatermarkImages,
  }) async {
    try {
      // 初始进度
      onProgress?.call(0.1);
      await Future.delayed(const Duration(milliseconds: 10)); // 让UI有机会更新

      // 读取原始图像
      final originalBytes = await originalImage.readAsBytes();
      onProgress?.call(0.2);
      await Future.delayed(const Duration(milliseconds: 10));

      // 准备水印数据
      final watermarkDataList = <WatermarkProcessData>[];

      for (int i = 0; i < watermarks.length; i++) {
        final watermark = watermarks[i];

        // 优先使用缓存的图像
        img.Image? watermarkImg = cachedWatermarkImages?[watermark.url];

        // 如果缓存中没有，则下载
        if (watermarkImg == null) {
          watermarkImg = await _downloadAndDecodeWatermark(watermark.url);
        }

        if (watermarkImg != null) {
          // 将img.Image转换为字节数据
          final watermarkBytes = Uint8List.fromList(
            img.encodePng(watermarkImg),
          );

          watermarkDataList.add(
            WatermarkProcessData(
              imageBytes: watermarkBytes,
              position: watermark.position,
              scale: watermark.scale,
              rotation: watermark.rotation,
              opacity: watermark.opacity,
            ),
          );
        }

        onProgress?.call(0.2 + (i + 1) / watermarks.length * 0.3);
        await Future.delayed(const Duration(milliseconds: 5)); // 让UI有机会更新
      }

      onProgress?.call(0.5);
      await Future.delayed(const Duration(milliseconds: 10));

      // 准备compute数据
      final compositeData = WatermarkCompositeData(
        originalImageBytes: originalBytes,
        watermarks: watermarkDataList,
        previewSize: previewSize,
        actualPreviewSize: actualPreviewSize,
        previewOffset: previewOffset,
        imageSize: imageSize,
      );

      onProgress?.call(0.6);
      await Future.delayed(const Duration(milliseconds: 10));

      // 在独立isolate中执行图像合成
      final compositeBytes = await compute(
        _processWatermarkComposite,
        compositeData,
      );

      onProgress?.call(0.9);
      await Future.delayed(const Duration(milliseconds: 10));

      // 保存合成图像
      final outputFile = await _saveCompositeImageFromBytes(compositeBytes);

      onProgress?.call(1.0);

      return outputFile;
    } catch (e) {
      throw Exception('图像合成失败: $e');
    }
  }

  // 优化的水印合成方法（所见即所得）
  Future<void> _addWatermarkToImageOptimized(
    img.Image baseImage,
    WatermarkModel watermark,
    img.Image watermarkImg,
    Size previewSize,
    Size actualPreviewSize,
    Offset previewOffset,
    Size imageSize,
    DebugProvider? debugProvider,
  ) async {
    try {
      // 直接使用CameraScreen传入的实际预览尺寸和偏移，确保100%一致
      // 不再重新计算，避免任何精度差异

      // 计算预览到图像的缩放比例
      // 直接使用图像尺寸和实际预览尺寸的比例
      final scaleX = imageSize.width / actualPreviewSize.width;
      final scaleY = imageSize.height / actualPreviewSize.height;

      // 调试输出辅助方法
      void debugLog(String message) {
        if (debugProvider?.isDebugMode == true) {
          debugPrint(message);
        }
      }

      debugLog('=== 水印合成调试信息（使用CameraScreen传入的尺寸） ===');
      debugLog('预览容器尺寸: ${previewSize.width} x ${previewSize.height}');
      debugLog(
        '实际预览尺寸（CameraScreen传入）: ${actualPreviewSize.width} x ${actualPreviewSize.height}',
      );
      debugLog(
        '预览偏移（CameraScreen传入）: (${previewOffset.dx}, ${previewOffset.dy})',
      );
      debugLog('图像尺寸: ${imageSize.width} x ${imageSize.height}');
      debugLog('图像宽高比: ${imageSize.width / imageSize.height}');
      debugLog('预览宽高比: ${actualPreviewSize.width / actualPreviewSize.height}');
      debugLog('容器宽高比: ${previewSize.width / previewSize.height}');
      debugLog(
        '缩放比例: scaleX=${scaleX.toStringAsFixed(2)}, scaleY=${scaleY.toStringAsFixed(2)}',
      );

      debugPrint('相对位置偏移: ${watermark.position}');

      // 新的坐标系统：position现在表示相对于几何中心的偏移量
      // 计算几何中心在容器中的绝对位置（包含预览偏移，与CameraScreen保持一致）
      final geometricCenterX = previewOffset.dx + actualPreviewSize.width / 2;
      final geometricCenterY = previewOffset.dy + actualPreviewSize.height / 2;

      // 计算水印在容器中的绝对位置（几何中心 + 相对偏移）
      final watermarkCenterInContainerX =
          geometricCenterX + watermark.position.dx;
      final watermarkCenterInContainerY =
          geometricCenterY + watermark.position.dy;

      // 转换为相对于实际预览区域的坐标（用于后续缩放计算）
      final watermarkCenterInPreviewX =
          watermarkCenterInContainerX - previewOffset.dx;
      final watermarkCenterInPreviewY =
          watermarkCenterInContainerY - previewOffset.dy;

      debugPrint('几何中心（容器坐标）: ($geometricCenterX, $geometricCenterY)');
      debugPrint(
        '水印在容器中的绝对位置: ($watermarkCenterInContainerX, $watermarkCenterInContainerY)',
      );
      debugPrint(
        '水印在实际预览区域中的位置: ($watermarkCenterInPreviewX, $watermarkCenterInPreviewY)',
      );

      // 验证几何中心计算
      final expectedPreviewCenterX = actualPreviewSize.width / 2;
      final expectedPreviewCenterY = actualPreviewSize.height / 2;
      debugPrint(
        '期望的预览几何中心: ($expectedPreviewCenterX, $expectedPreviewCenterY)',
      );
      debugPrint(
        '实际预览中心偏移: X=${(watermarkCenterInPreviewX - expectedPreviewCenterX).toStringAsFixed(1)}, Y=${(watermarkCenterInPreviewY - expectedPreviewCenterY).toStringAsFixed(1)}',
      );

      // 检查水印是否在预览区域内（使用绝对位置检查，允许一定超出）
      final previewMargin = 50.0; // 允许超出50像素
      if (watermarkCenterInPreviewX < -previewMargin ||
          watermarkCenterInPreviewX > actualPreviewSize.width + previewMargin ||
          watermarkCenterInPreviewY < -previewMargin ||
          watermarkCenterInPreviewY >
              actualPreviewSize.height + previewMargin) {
        debugPrint(
          '水印超出预览区域，跳过合成 (绝对位置: $watermarkCenterInPreviewX, $watermarkCenterInPreviewY)',
        );
        return;
      }

      debugPrint('水印缩放: ${watermark.scale}');
      debugPrint('水印旋转: ${watermark.rotation}');

      // 获取水印在预览中的基础显示尺寸（与DraggableWatermark保持一致）
      final baseDisplaySize = _calculateBaseDisplaySize(
        watermarkImg,
        previewSize, // 使用完整容器尺寸，与DraggableWatermark保持一致
      );

      debugPrint(
        '基础显示尺寸: ${baseDisplaySize.width} x ${baseDisplaySize.height}',
      );

      // 应用用户的缩放设置
      final scaledDisplayWidth = baseDisplaySize.width * watermark.scale;
      final scaledDisplayHeight = baseDisplaySize.height * watermark.scale;

      debugPrint('缩放后显示尺寸: $scaledDisplayWidth x $scaledDisplayHeight');

      // 计算水印在最终图像中的基础尺寸（不考虑旋转）
      final finalWidth = (scaledDisplayWidth * scaleX).round();
      final finalHeight = (scaledDisplayHeight * scaleY).round();

      debugPrint('最终基础尺寸: $finalWidth x $finalHeight');

      // 简化处理流程：先调整到基础尺寸，然后旋转
      // 注意：maintainAspect可能导致尺寸不准确，改为false确保精确尺寸
      final baseSizedWatermark = img.copyResize(
        watermarkImg,
        width: finalWidth,
        height: finalHeight,
        maintainAspect: false, // 确保精确尺寸，避免宽度压缩
        interpolation: img.Interpolation.cubic,
      );

      debugPrint(
        '基础调整后水印尺寸: ${baseSizedWatermark.width} x ${baseSizedWatermark.height}',
      );
      debugPrint('水印旋转角度（弧度）: ${watermark.rotation}');
      debugPrint('水印旋转角度（度数）: ${watermark.rotation * 180 / 3.14159}');

      // 应用旋转（如果需要）- 使用更高质量的插值和角度阈值
      final rotationAngleDegrees = watermark.rotation * 180 / 3.14159;
      final rotatedWatermark =
          rotationAngleDegrees.abs() >
              0.1 // 只有角度大于0.1度才旋转
          ? img.copyRotate(
              baseSizedWatermark,
              angle: rotationAngleDegrees,
              interpolation: img.Interpolation.linear, // 使用线性插值，避免过度平滑
            )
          : baseSizedWatermark;

      debugPrint(
        '旋转角度阈值检查: ${rotationAngleDegrees.abs() > 0.1 ? "应用旋转" : "跳过旋转"}',
      );

      debugPrint(
        '旋转后水印尺寸: ${rotatedWatermark.width} x ${rotatedWatermark.height}',
      );

      // 计算水印中心在最终图像中的位置（使用正确的缩放比例）
      // 这样可以处理预览和图像宽高比不同的情况
      final finalCenterX = (watermarkCenterInPreviewX * scaleX).round();
      final finalCenterY = (watermarkCenterInPreviewY * scaleY).round();

      debugPrint('使用的缩放比例: scaleX=$scaleX, scaleY=$scaleY');
      debugPrint(
        '预览中心位置: ($watermarkCenterInPreviewX, $watermarkCenterInPreviewY)',
      );
      debugPrint('转换后图像中心位置: ($finalCenterX, $finalCenterY)');

      // 验证坐标转换的正确性
      final expectedCenterX = imageSize.width / 2;
      final expectedCenterY = imageSize.height / 2;
      final offsetFromExpectedX = finalCenterX - expectedCenterX;
      final offsetFromExpectedY = finalCenterY - expectedCenterY;
      debugPrint('期望的图像中心: ($expectedCenterX, $expectedCenterY)');
      debugPrint(
        '实际偏移: X=${offsetFromExpectedX.toStringAsFixed(1)}, Y=${offsetFromExpectedY.toStringAsFixed(1)}',
      );

      // 计算旋转后水印的左上角位置（从中心点计算）
      final finalX = finalCenterX - rotatedWatermark.width ~/ 2;
      final finalY = finalCenterY - rotatedWatermark.height ~/ 2;

      debugPrint('水印中心在图像中的位置: ($finalCenterX, $finalCenterY)');
      debugPrint('计算的左上角位置: ($finalX, $finalY)');
      debugPrint('图像尺寸: ${imageSize.width} x ${imageSize.height}');

      // 更宽松的边界检查：允许部分超出，但确保至少有一部分在图像内
      final clampedFinalX = finalX.clamp(
        -rotatedWatermark.width ~/ 2, // 允许左侧超出一半
        imageSize.width.toInt() - rotatedWatermark.width ~/ 2, // 允许右侧超出一半
      );
      final clampedFinalY = finalY.clamp(
        -rotatedWatermark.height ~/ 2, // 允许上方超出一半
        imageSize.height.toInt() - rotatedWatermark.height ~/ 2, // 允许下方超出一半
      );

      debugPrint('边界检查后位置: ($clampedFinalX, $clampedFinalY)');

      // 检查水印是否完全在图像外
      final isCompletelyOutside =
          clampedFinalX + rotatedWatermark.width <= 0 ||
          clampedFinalY + rotatedWatermark.height <= 0 ||
          clampedFinalX >= imageSize.width ||
          clampedFinalY >= imageSize.height;

      if (isCompletelyOutside) {
        debugPrint('警告：水印完全在图像外，跳过合成');
        return;
      }

      // 应用透明度
      final watermarkWithOpacity = _applyOpacity(
        rotatedWatermark,
        watermark.opacity,
      );

      debugPrint('开始合成水印到图像...');
      debugPrint('基础图像尺寸: ${baseImage.width} x ${baseImage.height}');
      debugPrint(
        '水印尺寸: ${watermarkWithOpacity.width} x ${watermarkWithOpacity.height}',
      );
      debugPrint('合成位置: ($clampedFinalX, $clampedFinalY)');

      // 合成到基础图像（使用修正后的坐标）
      try {
        img.compositeImage(
          baseImage,
          watermarkWithOpacity,
          dstX: clampedFinalX,
          dstY: clampedFinalY,
        );
        debugPrint('✅ 水印合成成功');
      } catch (e) {
        debugPrint('❌ 水印合成失败: $e');
        rethrow;
      }

      debugPrint('=== 水印合成完成 ===');
    } catch (e) {
      debugPrint('添加水印失败: $e');
    }
  }

  // 计算水印在预览中的基础显示尺寸（不包含用户缩放）
  Size _calculateBaseDisplaySize(img.Image watermarkImg, Size containerSize) {
    // 获取水印原始尺寸
    final originalSize = Size(
      watermarkImg.width.toDouble(),
      watermarkImg.height.toDouble(),
    );

    // 应用智能缩放逻辑（与DraggableWatermark中的逻辑完全一致）
    double displayWidth = originalSize.width;
    double displayHeight = originalSize.height;

    // 如果水印宽度大于容器宽度，则调整为容器宽度，高度等比缩放
    if (displayWidth > containerSize.width) {
      final widthScale = containerSize.width / displayWidth;
      displayWidth = containerSize.width;
      displayHeight = displayHeight * widthScale;
    }

    // 如果调整后的高度超出显示区域，则以高度为准，宽度等比缩放
    if (displayHeight > containerSize.height) {
      final heightScale = containerSize.height / displayHeight;
      displayHeight = containerSize.height;
      displayWidth = displayWidth * heightScale;
    }

    return Size(displayWidth, displayHeight);
  }

  // 下载并解码水印图像（优化版本）
  Future<img.Image?> _downloadAndDecodeWatermark(String url) async {
    try {
      // 使用cached_network_image的缓存机制
      final imageProvider = CachedNetworkImageProvider(url);
      final imageStream = imageProvider.resolve(const ImageConfiguration());

      // 等待图像加载完成
      final completer = Completer<ui.Image>();
      late ImageStreamListener listener;

      listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
        completer.complete(info.image);
        imageStream.removeListener(listener);
      });

      imageStream.addListener(listener);
      final uiImage = await completer.future;

      // 转换为字节数据
      final byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // 直接解码为img.Image
      return img.decodeImage(bytes);
    } catch (e) {
      debugPrint('下载水印图像失败: $e');
      return null;
    }
  }

  // 应用透明度
  img.Image _applyOpacity(img.Image image, double opacity) {
    if (opacity >= 1.0) return image;

    // 简化处理：暂时跳过透明度处理，直接返回原图像
    // 在后续版本中可以添加更复杂的透明度处理
    return image;
  }

  // 保存合成图像
  Future<File> _saveCompositeImage(img.Image image) async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_composite_${timestamp}_$uuid.jpg';
    final filePath = '${photoDir.path}/$fileName';

    // 编码为JPEG
    final jpegBytes = img.encodeJpg(image, quality: 95);

    // 写入文件
    final file = File(filePath);
    await file.writeAsBytes(jpegBytes);

    return file;
  }

  // 从字节数据保存合成图像
  Future<File> _saveCompositeImageFromBytes(Uint8List imageBytes) async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_composite_${timestamp}_$uuid.jpg';
    final filePath = '${photoDir.path}/$fileName';

    // 写入文件
    final file = File(filePath);
    await file.writeAsBytes(imageBytes);

    return file;
  }

  // 从Widget创建图像
  Future<ui.Image> captureWidgetAsImage(
    GlobalKey key, {
    double pixelRatio = 1.0,
  }) async {
    try {
      final RenderRepaintBoundary boundary =
          key.currentContext!.findRenderObject() as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      return image;
    } catch (e) {
      throw Exception('捕获Widget图像失败: $e');
    }
  }

  // 将ui.Image转换为File
  Future<File> saveUiImageAsFile(
    ui.Image image, {
    String? fileName,
    int quality = 95,
  }) async {
    try {
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // 如果需要JPEG格式，进行转换
      final img.Image? decodedImage = img.decodePng(bytes);
      if (decodedImage == null) {
        throw Exception('无法解码图像');
      }

      final jpegBytes = img.encodeJpg(decodedImage, quality: quality);

      // 保存文件
      final directory = await getApplicationDocumentsDirectory();
      final photoDir = Directory(
        '${directory.path}/${AppConstants.photoDirectory}',
      );

      if (!await photoDir.exists()) {
        await photoDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = const Uuid().v4().substring(0, 8);
      final finalFileName = fileName ?? 'captured_${timestamp}_$uuid.jpg';
      final filePath = '${photoDir.path}/$finalFileName';

      final file = File(filePath);
      await file.writeAsBytes(jpegBytes);

      return file;
    } catch (e) {
      throw Exception('保存图像文件失败: $e');
    }
  }

  // 获取图像尺寸
  Future<Size> getImageSize(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      return Size(image.width.toDouble(), image.height.toDouble());
    } catch (e) {
      throw Exception('获取图像尺寸失败: $e');
    }
  }

  // 压缩图像
  Future<File> compressImage(
    File imageFile, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      img.Image processedImage = image;

      // 调整尺寸
      if (maxWidth != null || maxHeight != null) {
        processedImage = img.copyResize(
          processedImage,
          width: maxWidth,
          height: maxHeight,
          maintainAspect: true,
        );
      }

      // 压缩
      final compressedBytes = img.encodeJpg(processedImage, quality: quality);

      // 保存压缩后的文件
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'compressed_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final compressedFile = File(filePath);
      await compressedFile.writeAsBytes(compressedBytes);

      return compressedFile;
    } catch (e) {
      throw Exception('压缩图像失败: $e');
    }
  }

  // 创建缩略图
  Future<File> createThumbnail(
    File imageFile, {
    int size = 200,
    int quality = 80,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 创建正方形缩略图
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: quality);

      // 保存缩略图
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'thumbnail_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final thumbnailFile = File(filePath);
      await thumbnailFile.writeAsBytes(thumbnailBytes);

      return thumbnailFile;
    } catch (e) {
      throw Exception('创建缩略图失败: $e');
    }
  }
}

// 用于compute函数的数据结构
class WatermarkCompositeData {
  final Uint8List originalImageBytes;
  final List<WatermarkProcessData> watermarks;
  final Size previewSize;
  final Size actualPreviewSize;
  final Offset previewOffset;
  final Size imageSize;

  WatermarkCompositeData({
    required this.originalImageBytes,
    required this.watermarks,
    required this.previewSize,
    required this.actualPreviewSize,
    required this.previewOffset,
    required this.imageSize,
  });
}

class WatermarkProcessData {
  final Uint8List imageBytes;
  final Offset position;
  final double scale;
  final double rotation;
  final double opacity;

  WatermarkProcessData({
    required this.imageBytes,
    required this.position,
    required this.scale,
    required this.rotation,
    required this.opacity,
  });
}

// 在独立isolate中执行的图像合成函数
Uint8List _processWatermarkComposite(WatermarkCompositeData data) {
  try {
    // 解码原始图像
    final originalImg = img.decodeImage(data.originalImageBytes);
    if (originalImg == null) {
      throw Exception('无法解码原始图像');
    }

    // 创建合成图像
    final compositeImg = img.Image.from(originalImg);

    // 处理每个水印
    for (final watermarkData in data.watermarks) {
      final watermarkImg = img.decodeImage(watermarkData.imageBytes);
      if (watermarkImg == null) continue;

      _addWatermarkToImageInIsolate(
        compositeImg,
        watermarkData,
        watermarkImg,
        data.previewSize,
        data.actualPreviewSize,
        data.previewOffset,
        data.imageSize,
      );
    }

    // 编码为JPEG
    return Uint8List.fromList(img.encodeJpg(compositeImg, quality: 95));
  } catch (e) {
    throw Exception('图像合成失败: $e');
  }
}

// 在isolate中添加水印的简化版本
void _addWatermarkToImageInIsolate(
  img.Image baseImage,
  WatermarkProcessData watermarkData,
  img.Image watermarkImg,
  Size previewSize,
  Size actualPreviewSize,
  Offset previewOffset,
  Size imageSize,
) {
  try {
    // 计算缩放比例
    final scaleX = imageSize.width / actualPreviewSize.width;
    final scaleY = imageSize.height / actualPreviewSize.height;

    // 计算基础显示尺寸
    final baseDisplaySize = _calculateBaseDisplaySizeInIsolate(
      watermarkImg,
      previewSize,
    );

    // 应用用户缩放
    final scaledDisplayWidth = baseDisplaySize.width * watermarkData.scale;
    final scaledDisplayHeight = baseDisplaySize.height * watermarkData.scale;

    // 计算最终尺寸
    final finalWidth = (scaledDisplayWidth * scaleX).round();
    final finalHeight = (scaledDisplayHeight * scaleY).round();

    // 调整水印尺寸
    final resizedWatermark = img.copyResize(
      watermarkImg,
      width: finalWidth,
      height: finalHeight,
      maintainAspect: false,
      interpolation: img.Interpolation.cubic,
    );

    // 应用旋转
    final rotationAngleDegrees = watermarkData.rotation * 180 / 3.14159;
    final rotatedWatermark = rotationAngleDegrees.abs() > 0.1
        ? img.copyRotate(
            resizedWatermark,
            angle: rotationAngleDegrees,
            interpolation: img.Interpolation.linear,
          )
        : resizedWatermark;

    // 计算位置
    final imageCenterX = imageSize.width / 2;
    final imageCenterY = imageSize.height / 2;

    final finalX =
        (imageCenterX +
                watermarkData.position.dx * scaleX -
                rotatedWatermark.width / 2)
            .round();
    final finalY =
        (imageCenterY +
                watermarkData.position.dy * scaleY -
                rotatedWatermark.height / 2)
            .round();

    // 边界检查
    final clampedFinalX = finalX.clamp(
      -(rotatedWatermark.width ~/ 2),
      imageSize.width.round(),
    );
    final clampedFinalY = finalY.clamp(
      -(rotatedWatermark.height ~/ 2),
      imageSize.height.round(),
    );

    // 合成到基础图像
    img.compositeImage(
      baseImage,
      rotatedWatermark,
      dstX: clampedFinalX,
      dstY: clampedFinalY,
    );
  } catch (e) {
    // 在isolate中无法使用debugPrint，忽略错误
  }
}

// 在isolate中计算基础显示尺寸
Size _calculateBaseDisplaySizeInIsolate(
  img.Image watermarkImg,
  Size containerSize,
) {
  final originalSize = Size(
    watermarkImg.width.toDouble(),
    watermarkImg.height.toDouble(),
  );

  double displayWidth = originalSize.width;
  double displayHeight = originalSize.height;

  if (displayWidth > containerSize.width) {
    final widthScale = containerSize.width / displayWidth;
    displayWidth = containerSize.width;
    displayHeight = displayHeight * widthScale;
  }

  if (displayHeight > containerSize.height) {
    final heightScale = containerSize.height / displayHeight;
    displayHeight = containerSize.height;
    displayWidth = displayWidth * heightScale;
  }

  return Size(displayWidth, displayHeight);
}
