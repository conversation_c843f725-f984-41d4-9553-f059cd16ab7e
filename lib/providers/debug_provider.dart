import 'package:flutter/material.dart';

/// 调试模式状态管理
/// 控制应用中所有调试相关UI元素的显示和隐藏
class DebugProvider extends ChangeNotifier {
  // 调试模式开关状态
  bool _isDebugMode = false;

  /// 获取当前调试模式状态
  bool get isDebugMode => _isDebugMode;

  /// 切换调试模式
  void toggleDebugMode() {
    _isDebugMode = !_isDebugMode;
    notifyListeners();
    
    // 输出调试模式状态变化
    debugPrint('🐛 调试模式已${_isDebugMode ? "开启" : "关闭"}');
  }

  /// 开启调试模式
  void enableDebugMode() {
    if (!_isDebugMode) {
      _isDebugMode = true;
      notifyListeners();
      debugPrint('🐛 调试模式已开启');
    }
  }

  /// 关闭调试模式
  void disableDebugMode() {
    if (_isDebugMode) {
      _isDebugMode = false;
      notifyListeners();
      debugPrint('🐛 调试模式已关闭');
    }
  }

  /// 条件性调试输出
  /// 只有在调试模式开启时才输出调试信息
  void debugLog(String message) {
    if (_isDebugMode) {
      debugPrint(message);
    }
  }

  /// 获取调试模式图标
  IconData get debugIcon => _isDebugMode ? Icons.bug_report : Icons.bug_report_outlined;

  /// 获取调试模式颜色
  Color get debugColor => _isDebugMode ? Colors.orange : Colors.grey;
}
