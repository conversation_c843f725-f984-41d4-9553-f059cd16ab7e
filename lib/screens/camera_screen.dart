import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:provider/provider.dart';
import 'package:image/image.dart' as img;
import '../providers/camera_provider.dart';
import '../providers/watermark_provider.dart';
import '../providers/gallery_provider.dart';
import '../providers/debug_provider.dart';
import '../services/camera_service.dart';
import '../services/image_service.dart';
import '../models/photo_model.dart';
import '../widgets/draggable_watermark.dart';
// import '../widgets/watermark_control_panel.dart'; // 移除水印控制面板
import '../widgets/watermark_selector.dart';
// import '../widgets/vertical_zoom_slider.dart'; // 移除缩放滑块
import '../utils/constants.dart';
import '../utils/permissions.dart';
import 'gallery_screen.dart';

// 相机主界面
class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final CameraService _cameraService = CameraService();
  // final ImageService _imageService = ImageService(); // 暂时不使用
  final GlobalKey _previewKey = GlobalKey();

  bool _isInitialized = false;
  Size _previewSize = Size.zero; // 容器尺寸
  Size _cameraPreviewSize = Size.zero; // 相机真实预览尺寸（从CameraAwesome获取）
  AnalysisPreview? _analysisPreview; // 存储AnalysisPreview对象
  Size _actualPreviewSize = Size.zero; // 实际相机预览区域尺寸（排除黑边）
  bool _showCaptureSuccess = false;
  double _initialZoom = 1.0;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  // 初始化应用
  Future<void> _initializeApp() async {
    try {
      // 检查权限
      final permissions = await PermissionUtils.checkAllPermissions();
      if (!permissions['camera']! || !permissions['storage']!) {
        await _requestPermissions();
      }

      // 初始化水印列表
      context.read<WatermarkProvider>().initializeWatermarks();

      // 初始化相册
      if (mounted) {
        await context.read<GalleryProvider>().initializeGallery(context);
      }

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      _showErrorDialog('初始化失败: $e');
    }
  }

  // 请求权限
  Future<void> _requestPermissions() async {
    final results = await PermissionUtils.requestAllPermissions();

    if (!results['camera']!) {
      await PermissionUtils.showCameraPermissionDialog(context);
      return;
    }

    if (!results['storage']!) {
      await PermissionUtils.showStoragePermissionDialog(context);
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      body: Stack(
        children: [
          // 相机预览
          _buildCameraPreview(),

          // 水印层
          _buildWatermarkLayer(),

          // 控制界面
          _buildControlsOverlay(),

          // 移除水印控制面板

          // 水印选择器
          const WatermarkSelector(),

          // 拍摄成功指示器
          if (_showCaptureSuccess)
            Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(Icons.check, color: Colors.white, size: 60),
              ),
            ),

          // 移除缩放控制滑块
        ],
      ),
    );
  }

  // 计算实际预览区域尺寸（基于真实相机预览尺寸和contain模式）
  Size _calculateActualPreviewSize(Size containerSize, Size cameraPreviewSize) {
    // 如果还没有获取到相机预览尺寸，返回零尺寸
    if (cameraPreviewSize == Size.zero) {
      return Size.zero;
    }

    // 使用真实的相机预览宽高比
    final double cameraAspectRatio =
        cameraPreviewSize.width / cameraPreviewSize.height;
    final double containerAspectRatio =
        containerSize.width / containerSize.height;

    double actualWidth, actualHeight;

    if (containerAspectRatio > cameraAspectRatio) {
      // 容器更宽，以高度为准
      actualHeight = containerSize.height;
      actualWidth = actualHeight * cameraAspectRatio;
    } else {
      // 容器更高，以宽度为准
      actualWidth = containerSize.width;
      actualHeight = actualWidth / cameraAspectRatio;
    }

    debugPrint('=== 预览尺寸计算 ===');
    debugPrint(
      '容器尺寸: ${containerSize.width.toStringAsFixed(1)} x ${containerSize.height.toStringAsFixed(1)}',
    );
    debugPrint(
      '相机预览尺寸: ${cameraPreviewSize.width.toStringAsFixed(1)} x ${cameraPreviewSize.height.toStringAsFixed(1)}',
    );
    debugPrint('容器宽高比: ${containerAspectRatio.toStringAsFixed(3)}');
    debugPrint('相机宽高比: ${cameraAspectRatio.toStringAsFixed(3)}');
    debugPrint(
      '计算结果: ${actualWidth.toStringAsFixed(1)} x ${actualHeight.toStringAsFixed(1)}',
    );

    return Size(actualWidth, actualHeight);
  }

  // 构建相机预览
  Widget _buildCameraPreview() {
    return RepaintBoundary(
      key: _previewKey,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 只在尺寸真正变化时更新（避免浮点数精度问题）
          final containerSize = Size(
            constraints.maxWidth,
            constraints.maxHeight,
          );

          if ((_previewSize.width - containerSize.width).abs() > 0.1 ||
              (_previewSize.height - containerSize.height).abs() > 0.1) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // 只有在获取到真实相机预览尺寸时才计算
              if (_cameraPreviewSize != Size.zero) {
                final actualSize = _calculateActualPreviewSize(
                  containerSize,
                  _cameraPreviewSize,
                );
                debugPrint(
                  '容器尺寸更新: ${containerSize.width}x${containerSize.height}',
                );
                debugPrint('实际预览尺寸: ${actualSize.width}x${actualSize.height}');
                setState(() {
                  _previewSize = containerSize;
                  _actualPreviewSize = actualSize;
                });
              }
            });
          }

          return GestureDetector(
            // 添加手势缩放功能
            onScaleStart: (details) {
              // 记录初始缩放值
              _initialZoom = context.read<CameraProvider>().zoomLevel;
              debugPrint(
                '👆 开始缩放手势，初始缩放值: ${_initialZoom.toStringAsFixed(2)}x, 触摸点数: ${details.pointerCount}',
              );
            },
            onScaleUpdate: (details) {
              debugPrint(
                '📱 缩放手势更新: scale=${details.scale.toStringAsFixed(3)}, 触摸点数: ${details.pointerCount}',
              );

              // 处理缩放手势（提高响应灵敏度）
              if (details.scale != 1.0) {
                final cameraProvider = context.read<CameraProvider>();
                // 基于初始缩放值和手势缩放比例计算新的缩放值
                final newZoom = (_initialZoom * details.scale).clamp(1.0, 8.0);
                final currentZoom = cameraProvider.zoomLevel;
                final zoomDiff = (newZoom - currentZoom).abs();

                debugPrint(
                  '📏 计算缩放: 初始=${_initialZoom.toStringAsFixed(2)}x, 手势scale=${details.scale.toStringAsFixed(3)}, 新值=${newZoom.toStringAsFixed(2)}x, 当前=${currentZoom.toStringAsFixed(2)}x, 差值=${zoomDiff.toStringAsFixed(3)}',
                );

                // 进一步降低更新阈值，确保UI实时响应
                if (zoomDiff > 0.01) {
                  debugPrint('✅ 触发缩放更新');
                  cameraProvider.setZoomLevel(newZoom);
                } else {
                  debugPrint('⏭️ 跳过缩放更新（差值太小）');
                }
              } else {
                debugPrint('⏭️ 跳过缩放更新（scale=1.0）');
              }
            },
            onScaleEnd: (details) {
              // 缩放结束，更新初始缩放值
              final finalZoom = context.read<CameraProvider>().zoomLevel;
              _initialZoom = finalZoom;
              debugPrint('🏁 缩放手势结束，最终缩放值: ${finalZoom.toStringAsFixed(2)}x');
            },
            // 添加点击检测来验证手势检测器是否工作
            onTap: () {
              debugPrint('👆 检测到点击事件');
            },
            child: Container(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              color: Colors.black,
              child: Stack(
                children: [
                  // 相机容器层边框（黄色）- 调试模式控制
                  Consumer<DebugProvider>(
                    builder: (context, debugProvider, child) {
                      if (!debugProvider.isDebugMode)
                        return const SizedBox.shrink();
                      return Positioned.fill(
                        child: IgnorePointer(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.yellow,
                                width: 2.0,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  // 相机容器层信息面板（黄色）- 调试模式控制
                  Consumer<DebugProvider>(
                    builder: (context, debugProvider, child) {
                      if (!debugProvider.isDebugMode)
                        return const SizedBox.shrink();
                      return Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          color: Colors.yellow.withValues(alpha: 0.9),
                          child: Text(
                            '容器层 ${constraints.maxWidth.toStringAsFixed(1)}x${constraints.maxHeight.toStringAsFixed(1)} (0,0)',
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  // 相机预览（前置摄像头时镜像）
                  Consumer<CameraProvider>(
                    builder: (context, cameraProvider, child) {
                      // 创建变换矩阵
                      final transform = cameraProvider.isFrontCamera
                          ? (Matrix4.identity()
                              ..scale(-1.0, 1.0, 1.0)) // 前置摄像头水平翻转
                          : Matrix4.identity();

                      return Transform(
                        alignment: Alignment.center,
                        transform: transform,
                        child: CameraAwesomeBuilder.custom(
                          saveConfig: _cameraService.getSaveConfig(),
                          sensorConfig: _cameraService.getSensorConfig(),
                          previewFit:
                              CameraPreviewFit.contain, // 改为contain模式，完整显示不裁剪
                          onMediaCaptureEvent: _cameraService
                              .createMediaCaptureHandler(
                                onPhotoTaken: _onPhotoTaken,
                                onVideoRecorded: _onVideoRecorded,
                                onError: _onCaptureError,
                              ),
                          builder: (cameraState, previewSize) {
                            // 更新相机真实预览尺寸
                            if (_analysisPreview != previewSize) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  _analysisPreview = previewSize;
                                  // 尝试从AnalysisPreview获取尺寸信息
                                  debugPrint('=== AnalysisPreview 信息 ===');
                                  debugPrint(
                                    'previewSize类型: ${previewSize.runtimeType}',
                                  );
                                  debugPrint('previewSize内容: $previewSize');

                                  // 尝试访问AnalysisPreview的属性
                                  try {
                                    final dynamic dynamicPreview = previewSize;
                                    debugPrint('=== AnalysisPreview 属性访问 ===');

                                    // 尝试访问nativePreviewSize属性
                                    try {
                                      final nativeSize =
                                          dynamicPreview.nativePreviewSize;
                                      debugPrint(
                                        'nativePreviewSize: $nativeSize',
                                      );
                                      if (nativeSize != null) {
                                        _cameraPreviewSize = nativeSize as Size;
                                        debugPrint(
                                          '成功获取相机原生预览尺寸: $_cameraPreviewSize',
                                        );
                                      }
                                    } catch (e) {
                                      debugPrint('访问nativePreviewSize失败: $e');
                                    }

                                    // 尝试访问previewSize属性
                                    try {
                                      final displaySize =
                                          dynamicPreview.previewSize;
                                      debugPrint(
                                        'previewSize (显示尺寸): $displaySize',
                                      );
                                    } catch (e) {
                                      debugPrint('访问previewSize失败: $e');
                                    }

                                    // 尝试访问其他属性
                                    try {
                                      debugPrint(
                                        'offset: ${dynamicPreview.offset}',
                                      );
                                      debugPrint(
                                        'scale: ${dynamicPreview.scale}',
                                      );
                                      debugPrint(
                                        'sensor: ${dynamicPreview.sensor}',
                                      );
                                    } catch (e) {
                                      debugPrint('访问其他属性失败: $e');
                                    }
                                  } catch (e) {
                                    debugPrint('访问AnalysisPreview属性失败: $e');
                                  }

                                  // 重新计算实际预览尺寸
                                  if (_previewSize != Size.zero &&
                                      _cameraPreviewSize != Size.zero) {
                                    _actualPreviewSize =
                                        _calculateActualPreviewSize(
                                          _previewSize,
                                          _cameraPreviewSize,
                                        );
                                  }
                                });
                              });
                            }

                            // 只在相机状态真正变化时初始化相机提供者
                            final cameraProvider = context
                                .read<CameraProvider>();
                            if (!cameraProvider.isInitialized) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                cameraProvider.initializeCamera(cameraState);
                              });
                            }

                            // 根据相机状态返回不同的UI
                            return cameraState.when(
                              onPreparingCamera: (state) => const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              ),
                              onPhotoMode: (state) => const SizedBox.shrink(),
                              onVideoMode: (state) => const SizedBox.shrink(),
                              onVideoRecordingMode: (state) =>
                                  const SizedBox.shrink(),
                            );
                          },
                        ),
                      );
                    },
                  ),

                  // 拍摄时的固定画面覆盖层
                  Consumer<CameraProvider>(
                    builder: (context, cameraProvider, child) {
                      if (cameraProvider.isCapturing &&
                          cameraProvider.capturedFrame != null) {
                        return Positioned.fill(
                          child: Image.memory(
                            cameraProvider.capturedFrame!,
                            fit: BoxFit.contain,
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 构建水印层
  Widget _buildWatermarkLayer() {
    return Consumer<WatermarkProvider>(
      builder: (context, watermarkProvider, child) {
        if (_previewSize == Size.zero) return const SizedBox.shrink();

        // 计算实际预览区域尺寸和偏移
        // 只有在获取到真实相机预览尺寸时才计算
        if (_cameraPreviewSize == Size.zero) {
          // 如果还没有获取到相机预览尺寸，返回空的水印层
          return const SizedBox.shrink();
        }

        final actualPreviewSize = _calculateActualPreviewSize(
          _previewSize,
          _cameraPreviewSize,
        );
        final previewOffset = Offset(
          (_previewSize.width - actualPreviewSize.width) / 2,
          (_previewSize.height - actualPreviewSize.height) / 2,
        );

        // 调试信息 - 使用DebugProvider控制
        Consumer<DebugProvider>(
          builder: (context, debugProvider, child) {
            if (debugProvider.isDebugMode) {
              debugProvider.debugLog('=== 水印层调试信息 ===');
              debugProvider.debugLog(
                '容器尺寸: ${_previewSize.width} x ${_previewSize.height}',
              );
              debugProvider.debugLog(
                '实际预览尺寸: ${actualPreviewSize.width} x ${actualPreviewSize.height}',
              );
              debugProvider.debugLog(
                '预览偏移: ${previewOffset.dx} x ${previewOffset.dy}',
              );
              debugProvider.debugLog(
                '几何中心位置: ${previewOffset.dx + actualPreviewSize.width / 2} x ${previewOffset.dy + actualPreviewSize.height / 2}',
              );
            }
            return const SizedBox.shrink();
          },
        );

        // 减少调试信息输出频率
        // debugPrint('水印层容器尺寸: ${_previewSize.width}x${_previewSize.height}');
        // debugPrint('实际预览尺寸: ${actualPreviewSize.width}x${actualPreviewSize.height}');
        // debugPrint('预览偏移: ${previewOffset.dx}x${previewOffset.dy}');

        return SizedBox(
          width: _previewSize.width,
          height: _previewSize.height,
          child: Stack(
            children: [
              // 水印层边框（红色）- 调试模式控制
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  if (!debugProvider.isDebugMode)
                    return const SizedBox.shrink();
                  return Positioned.fill(
                    child: IgnorePointer(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red, width: 2.0),
                        ),
                      ),
                    ),
                  );
                },
              ),
              // 水印层信息面板（红色）- 调试模式控制
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  if (!debugProvider.isDebugMode)
                    return const SizedBox.shrink();
                  return Positioned(
                    left: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      color: Colors.red.withValues(alpha: 0.9),
                      child: Text(
                        '水印层 ${_previewSize.width.toStringAsFixed(1)}x${_previewSize.height.toStringAsFixed(1)} (0,0)',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
              // 水印层
              ...watermarkProvider.activeWatermarks.map((watermark) {
                // 减少调试信息输出频率
                // debugPrint(
                //   '水印 ${watermark.id} 位置: (${watermark.position.dx}, ${watermark.position.dy}), 缩放: ${watermark.scale}',
                // );
                return DraggableWatermark(
                  key: ValueKey(watermark.id),
                  watermark: watermark,
                  containerSize: _previewSize,
                  actualPreviewSize: actualPreviewSize,
                  previewOffset: previewOffset,
                  onTap: () {
                    // 点击水印时的处理
                  },
                  onDoubleTap: () {
                    // 双击删除水印
                    watermarkProvider.removeWatermark(watermark.id);
                  },
                  onLongPress: () {
                    // 长按复制水印
                    watermarkProvider.duplicateWatermark(watermark.id);
                  },
                );
              }),
              // 相机预览层边框（绿色）- 调试模式控制
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  if (!debugProvider.isDebugMode)
                    return const SizedBox.shrink();
                  return Positioned(
                    left: previewOffset.dx,
                    top: previewOffset.dy,
                    child: IgnorePointer(
                      child: Container(
                        width: actualPreviewSize.width,
                        height: actualPreviewSize.height,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.green, width: 2.0),
                        ),
                      ),
                    ),
                  );
                },
              ),
              // 相机预览层信息面板（绿色）- 调试模式控制
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  if (!debugProvider.isDebugMode)
                    return const SizedBox.shrink();
                  return Positioned(
                    left: previewOffset.dx,
                    top: previewOffset.dy,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      color: Colors.green.withValues(alpha: 0.9),
                      child: Text(
                        '预览层 ${actualPreviewSize.width.toStringAsFixed(1)}x${actualPreviewSize.height.toStringAsFixed(1)} (${previewOffset.dx.toStringAsFixed(1)},${previewOffset.dy.toStringAsFixed(1)})',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
              // 🔴 坐标轴系统 - 调试模式控制
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  if (!debugProvider.isDebugMode)
                    return const SizedBox.shrink();
                  return Stack(
                    children: [
                      // X轴（水平线）
                      Positioned(
                        left: previewOffset.dx,
                        top:
                            previewOffset.dy + actualPreviewSize.height / 2 - 1,
                        child: IgnorePointer(
                          child: Container(
                            width: actualPreviewSize.width,
                            height: 2,
                            color: Colors.red.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                      // Y轴（垂直线）
                      Positioned(
                        left:
                            previewOffset.dx + actualPreviewSize.width / 2 - 1,
                        top: previewOffset.dy,
                        child: IgnorePointer(
                          child: Container(
                            width: 2,
                            height: actualPreviewSize.height,
                            color: Colors.red.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                      // 几何中心原点标记
                      Positioned(
                        left:
                            previewOffset.dx + actualPreviewSize.width / 2 - 6,
                        top:
                            previewOffset.dy + actualPreviewSize.height / 2 - 6,
                        child: IgnorePointer(
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                          ),
                        ),
                      ),
                      // 几何中心坐标标签
                      Positioned(
                        left:
                            previewOffset.dx + actualPreviewSize.width / 2 + 15,
                        top:
                            previewOffset.dy +
                            actualPreviewSize.height / 2 -
                            15,
                        child: IgnorePointer(
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            color: Colors.red.withValues(alpha: 0.9),
                            child: Text(
                              '几何中心\n原点(0,0)',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                      // X轴标签
                      Positioned(
                        left: previewOffset.dx + actualPreviewSize.width - 30,
                        top:
                            previewOffset.dy + actualPreviewSize.height / 2 + 5,
                        child: IgnorePointer(
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            color: Colors.red.withValues(alpha: 0.8),
                            child: Text(
                              '+X',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Y轴标签
                      Positioned(
                        left:
                            previewOffset.dx + actualPreviewSize.width / 2 + 5,
                        top: previewOffset.dy + actualPreviewSize.height - 25,
                        child: IgnorePointer(
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            color: Colors.red.withValues(alpha: 0.8),
                            child: Text(
                              '+Y',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建控制界面
  Widget _buildControlsOverlay() {
    return SafeArea(
      child: Column(
        children: [
          // 顶部控制栏
          _buildTopControls(),

          const Spacer(),

          // 底部控制栏
          _buildBottomControls(),
        ],
      ),
    );
  }

  // 构建顶部控制栏
  Widget _buildTopControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 水印按钮
          Consumer<WatermarkProvider>(
            builder: (context, watermarkProvider, child) {
              return IconButton(
                onPressed: () {
                  watermarkProvider.toggleWatermarkSelector();
                },
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    watermarkProvider.isWatermarkSelectorVisible
                        ? Icons.close
                        : Icons.add_photo_alternate,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),

          // 右侧控制组
          Row(
            children: [
              // Debug模式开关
              Consumer<DebugProvider>(
                builder: (context, debugProvider, child) {
                  return IconButton(
                    onPressed: () {
                      debugProvider.toggleDebugMode();
                    },
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        debugProvider.debugIcon,
                        color: debugProvider.debugColor,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(width: 8),

              // 闪光灯控制
              Consumer<CameraProvider>(
                builder: (context, cameraProvider, child) {
                  return IconButton(
                    onPressed: () {
                      cameraProvider.toggleFlashMode();
                    },
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        _getFlashIcon(cameraProvider.flashMode),
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 移除缩放控制滑块方法

  // 构建底部控制栏
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 相册按钮
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const GalleryScreen()),
              );
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: const Icon(
                Icons.photo_library,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),

          // 拍照按钮
          Consumer<CameraProvider>(
            builder: (context, cameraProvider, child) {
              return GestureDetector(
                onTap: () {
                  _takePhoto();
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(40),
                    border: Border.all(color: AppColors.primary, width: 4),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: AppColors.primary,
                    size: 40,
                  ),
                ),
              );
            },
          ),

          // 切换摄像头按钮
          Consumer<CameraProvider>(
            builder: (context, cameraProvider, child) {
              return GestureDetector(
                onTap: () {
                  cameraProvider.switchCamera();
                },
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: const Icon(
                    Icons.flip_camera_ios,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // 移除水印控制面板功能

  // 获取闪光灯图标
  IconData _getFlashIcon(FlashMode flashMode) {
    switch (flashMode) {
      case FlashMode.none:
        return Icons.flash_off;
      case FlashMode.on:
        return Icons.flash_on;
      case FlashMode.auto:
        return Icons.flash_auto;
      default:
        return Icons.flash_auto;
    }
  }

  // 拍照处理
  Future<void> _takePhoto() async {
    try {
      final cameraProvider = context.read<CameraProvider>();

      // 截取当前预览画面
      await _capturePreviewFrame();

      // 执行拍照
      await cameraProvider.takePhoto();
    } catch (e) {
      _showErrorDialog('拍照失败: $e');
    }
  }

  // 截取预览画面
  Future<void> _capturePreviewFrame() async {
    try {
      if (_previewKey.currentContext != null) {
        final RenderRepaintBoundary boundary =
            _previewKey.currentContext!.findRenderObject()
                as RenderRepaintBoundary;

        final ui.Image image = await boundary.toImage(pixelRatio: 1.0);
        final ByteData? byteData = await image.toByteData(
          format: ui.ImageByteFormat.png,
        );

        if (byteData != null) {
          final Uint8List frameData = byteData.buffer.asUint8List();

          // 设置拍摄状态并保存截取的画面
          if (mounted) {
            context.read<CameraProvider>().startCapturing(frameData);
          }
        }
      }
    } catch (e) {
      debugPrint('截取预览画面失败: $e');
      // 即使截取失败也继续拍照流程
      if (mounted) {
        context.read<CameraProvider>().startCapturing(null);
      }
    }
  }

  // 照片拍摄完成回调
  void _onPhotoTaken(photoModel) async {
    try {
      // 类型检查并获取照片路径
      if (photoModel is! PhotoModel) {
        throw Exception('无效的照片模型类型');
      }

      final String photoPath = photoModel.filePath;

      debugPrint('照片模型: $photoModel');
      debugPrint('照片路径: $photoPath');

      // 检查文件路径是否有效
      if (photoPath.isEmpty) {
        throw Exception('照片路径为空');
      }

      // 检查是否有活跃的水印需要合成
      final watermarkProvider = context.read<WatermarkProvider>();
      String finalPhotoPath = photoPath;

      if (watermarkProvider.hasActiveWatermarks) {
        debugPrint(
          '检测到 ${watermarkProvider.activeWatermarks.length} 个活跃水印，开始合成...',
        );

        // 显示合成进度对话框
        if (mounted) {
          _showCompositeProgressDialog();
        }

        try {
          // 获取图像尺寸
          final originalFile = File(photoPath);
          final imageSize = await _getImageSize(originalFile);

          // 输出调试信息
          debugPrint('=== 拍照调试信息 ===');
          debugPrint('预览窗口尺寸: ${_previewSize.width} x ${_previewSize.height}');
          debugPrint('拍照图片尺寸: ${imageSize.width} x ${imageSize.height}');
          debugPrint('图片宽高比: ${imageSize.width / imageSize.height}');
          debugPrint('预览宽高比: ${_previewSize.width / _previewSize.height}');
          debugPrint('活跃水印数量: ${watermarkProvider.activeWatermarks.length}');

          // 计算实际预览尺寸和偏移（与水印层使用相同的计算）
          final actualPreviewSize = _calculateActualPreviewSize(
            _previewSize,
            _cameraPreviewSize,
          );
          final previewOffset = Offset(
            (_previewSize.width - actualPreviewSize.width) / 2,
            (_previewSize.height - actualPreviewSize.height) / 2,
          );

          debugPrint('=== 传递给ImageService的参数 ===');
          debugPrint('预览容器尺寸: ${_previewSize.width} x ${_previewSize.height}');
          debugPrint(
            '实际预览尺寸: ${actualPreviewSize.width} x ${actualPreviewSize.height}',
          );
          debugPrint('预览偏移: (${previewOffset.dx}, ${previewOffset.dy})');
          debugPrint('图像尺寸: ${imageSize.width} x ${imageSize.height}');

          // 进行水印合成
          final imageService = ImageService();
          final compositeFile = await imageService.compositeImageWithWatermarks(
            originalImage: originalFile,
            watermarks: watermarkProvider.activeWatermarks,
            previewSize: _previewSize,
            actualPreviewSize: actualPreviewSize,
            previewOffset: previewOffset,
            imageSize: imageSize,
            debugProvider: context.read<DebugProvider>(),
            onProgress: (progress) {
              // 更新进度（如果对话框仍然显示）
              if (mounted) {
                final debugProvider = context.read<DebugProvider>();
                if (debugProvider.isDebugMode) {
                  debugPrint('合成进度: ${(progress * 100).toInt()}%');
                }
              }
            },
          );

          finalPhotoPath = compositeFile.path;
          debugPrint('水印合成完成: $finalPhotoPath');
        } catch (e) {
          debugPrint('水印合成失败: $e');
          // 合成失败时使用原始照片
          finalPhotoPath = photoPath;
        } finally {
          // 关闭进度对话框
          if (mounted) {
            Navigator.of(context).pop();
          }
        }
      } else {
        debugPrint('没有活跃水印，使用原始照片');
      }

      // 保存到系统相册
      await CameraService.savePhotoToGallery(finalPhotoPath);

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 创建新的PhotoModel（如果路径发生了变化）
      PhotoModel finalPhotoModel = photoModel;
      if (finalPhotoPath != photoPath) {
        final finalFile = File(finalPhotoPath);
        final stat = await finalFile.stat();
        finalPhotoModel = photoModel.copyWith(
          filePath: finalPhotoPath,
          fileName: finalPhotoPath.split('/').last,
          fileSize: stat.size,
        );
      }

      // 添加到应用相册
      context.read<GalleryProvider>().addPhoto(finalPhotoModel);

      // 结束拍摄状态，恢复预览
      context.read<CameraProvider>().endCapturing();

      // 显示拍摄成功指示器
      _showCaptureSuccessIndicator();

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                watermarkProvider.hasActiveWatermarks
                    ? '带水印照片已保存到相册'
                    : '照片已保存到相册',
              ),
            ],
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.black87,
        ),
      );
    } catch (e) {
      debugPrint('保存照片失败: $e');

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 结束拍摄状态，恢复预览（即使出错也要恢复）
      context.read<CameraProvider>().endCapturing();

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(width: 8),
              Expanded(
                child: Text('保存失败: $e', overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red.shade800,
        ),
      );
    }
  }

  // 显示拍摄成功指示器
  void _showCaptureSuccessIndicator() {
    setState(() {
      _showCaptureSuccess = true;
    });

    // 2秒后隐藏
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showCaptureSuccess = false;
        });
      }
    });
  }

  // 视频录制完成回调
  void _onVideoRecorded(String videoPath) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('视频保存成功'), duration: Duration(seconds: 2)),
    );
  }

  // 捕获错误回调
  void _onCaptureError(String error) {
    _showErrorDialog(error);
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 显示合成进度对话框
  void _showCompositeProgressDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('正在合成水印...'),
            const SizedBox(height: 8),
            Text(
              '请稍候，正在处理您的照片',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  // 获取图像尺寸
  Future<Size> _getImageSize(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image != null) {
        return Size(image.width.toDouble(), image.height.toDouble());
      } else {
        // 如果解码失败，返回默认尺寸
        debugPrint('无法解码图像，使用默认尺寸');
        return const Size(1920, 1080);
      }
    } catch (e) {
      debugPrint('获取图像尺寸失败: $e');
      // 返回默认尺寸
      return const Size(1920, 1080);
    }
  }
}
