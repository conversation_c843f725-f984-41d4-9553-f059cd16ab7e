import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:watermark_camera/providers/debug_provider.dart';

void main() {
  group('DebugProvider Tests', () {
    late DebugProvider debugProvider;

    setUp(() {
      debugProvider = DebugProvider();
    });

    test('初始状态应该是调试模式关闭', () {
      expect(debugProvider.isDebugMode, false);
    });

    test('toggleDebugMode 应该切换调试模式状态', () {
      // 初始状态：关闭
      expect(debugProvider.isDebugMode, false);

      // 切换到开启
      debugProvider.toggleDebugMode();
      expect(debugProvider.isDebugMode, true);

      // 再次切换到关闭
      debugProvider.toggleDebugMode();
      expect(debugProvider.isDebugMode, false);
    });

    test('enableDebugMode 应该开启调试模式', () {
      debugProvider.enableDebugMode();
      expect(debugProvider.isDebugMode, true);
    });

    test('disableDebugMode 应该关闭调试模式', () {
      debugProvider.enableDebugMode(); // 先开启
      debugProvider.disableDebugMode(); // 再关闭
      expect(debugProvider.isDebugMode, false);
    });

    test('debugIcon 应该根据调试模式返回正确的图标', () {
      // 调试模式关闭时
      expect(debugProvider.debugIcon, Icons.bug_report_outlined);

      // 调试模式开启时
      debugProvider.enableDebugMode();
      expect(debugProvider.debugIcon, Icons.bug_report);
    });

    test('debugColor 应该根据调试模式返回正确的颜色', () {
      // 调试模式关闭时
      expect(debugProvider.debugColor, Colors.grey);

      // 调试模式开启时
      debugProvider.enableDebugMode();
      expect(debugProvider.debugColor, Colors.orange);
    });

    test('debugLog 应该在调试模式开启时输出日志', () {
      // 这个测试主要验证方法不会抛出异常
      debugProvider.enableDebugMode();
      expect(() => debugProvider.debugLog('测试日志'), returnsNormally);

      debugProvider.disableDebugMode();
      expect(() => debugProvider.debugLog('测试日志'), returnsNormally);
    });
  });
}
